package com.ruoyi.project.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.biz.domain.BizEngineeringFiles;
import com.ruoyi.project.biz.service.IBizEngineeringFilesService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 工程数量文件Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/biz/engineeringFile")
public class BizEngineeringFilesController extends BaseController
{
    @Autowired
    private IBizEngineeringFilesService bizEngineeringFilesService;

    /**
     * 查询工程数量文件列表
     */
    @PreAuthorize("@ss.hasPermi('biz:engineeringFile:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizEngineeringFiles bizEngineeringFiles)
    {
        startPage();
        List<BizEngineeringFiles> list = bizEngineeringFilesService.selectBizEngineeringFilesList(bizEngineeringFiles);
        return getDataTable(list);
    }

    /**
     * 导出工程数量文件列表
     */
    @PreAuthorize("@ss.hasPermi('biz:engineeringFile:export')")
    @Log(title = "工程数量文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizEngineeringFiles bizEngineeringFiles)
    {
        List<BizEngineeringFiles> list = bizEngineeringFilesService.selectBizEngineeringFilesList(bizEngineeringFiles);
        ExcelUtil<BizEngineeringFiles> util = new ExcelUtil<BizEngineeringFiles>(BizEngineeringFiles.class);
        util.exportExcel(response, list, "工程数量文件数据");
    }

    /**
     * 获取工程数量文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('biz:engineeringFile:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizEngineeringFilesService.selectBizEngineeringFilesById(id));
    }

    /**
     * 新增工程数量文件
     */
    @PreAuthorize("@ss.hasPermi('biz:engineeringFile:add')")
    @Log(title = "工程数量文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizEngineeringFiles bizEngineeringFiles)
    {
        return toAjax(bizEngineeringFilesService.insertBizEngineeringFiles(bizEngineeringFiles));
    }

    /**
     * 修改工程数量文件
     */
    @PreAuthorize("@ss.hasPermi('biz:engineeringFile:edit')")
    @Log(title = "工程数量文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizEngineeringFiles bizEngineeringFiles)
    {
        return toAjax(bizEngineeringFilesService.updateBizEngineeringFiles(bizEngineeringFiles));
    }

    /**
     * 删除工程数量文件
     */
    @PreAuthorize("@ss.hasPermi('biz:engineeringFile:remove')")
    @Log(title = "工程数量文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizEngineeringFilesService.deleteBizEngineeringFilesByIds(ids));
    }
}

package com.ruoyi.project.biz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.biz.domain.BizProject;
import com.ruoyi.project.biz.service.IBizProjectService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 项目管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/biz/project")
public class BizProjectController extends BaseController
{
    @Autowired
    private IBizProjectService bizProjectService;

    /**
     * 查询项目管理列表
     */
    @PreAuthorize("@ss.hasPermi('biz:project:list')")
    @GetMapping("/list")
    public TableDataInfo list(BizProject bizProject)
    {
        startPage();
        List<BizProject> list = bizProjectService.selectBizProjectList(bizProject);
        return getDataTable(list);
    }

    /**
     * 导出项目管理列表
     */
    @PreAuthorize("@ss.hasPermi('biz:project:export')")
    @Log(title = "项目管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizProject bizProject)
    {
        List<BizProject> list = bizProjectService.selectBizProjectList(bizProject);
        ExcelUtil<BizProject> util = new ExcelUtil<BizProject>(BizProject.class);
        util.exportExcel(response, list, "项目管理数据");
    }

    /**
     * 获取项目管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('biz:project:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bizProjectService.selectBizProjectById(id));
    }

    /**
     * 新增项目管理
     */
    @PreAuthorize("@ss.hasPermi('biz:project:add')")
    @Log(title = "项目管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizProject bizProject)
    {
        return toAjax(bizProjectService.insertBizProject(bizProject));
    }

    /**
     * 修改项目管理
     */
    @PreAuthorize("@ss.hasPermi('biz:project:edit')")
    @Log(title = "项目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizProject bizProject)
    {
        return toAjax(bizProjectService.updateBizProject(bizProject));
    }

    /**
     * 删除项目管理
     */
    @PreAuthorize("@ss.hasPermi('biz:project:remove')")
    @Log(title = "项目管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bizProjectService.deleteBizProjectByIds(ids));
    }
}

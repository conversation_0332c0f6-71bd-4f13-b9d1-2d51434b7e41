package com.ruoyi.project.biz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 工程数量文件对象 biz_engineering_files
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public class BizEngineeringFiles extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 关联的项目ID */
    @Excel(name = "关联的项目ID")
    private Long projectId;

    /** 所属分类 */
    @Excel(name = "所属分类")
    private Long categoryType;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件存储路径 */
    @Excel(name = "文件存储路径")
    private String filePath;

    /** 文件类型  */
    @Excel(name = "文件类型 ")
    private String fileType;

    /** 文件大小 (bytes) */
    @Excel(name = "文件大小 (bytes)")
    private Long fileSizeBytes;

    /** 文件备注 */
    @Excel(name = "文件备注")
    private String remarks;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }

    public void setCategoryType(Long categoryType) 
    {
        this.categoryType = categoryType;
    }

    public Long getCategoryType() 
    {
        return categoryType;
    }

    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }

    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public void setFileType(String fileType) 
    {
        this.fileType = fileType;
    }

    public String getFileType() 
    {
        return fileType;
    }

    public void setFileSizeBytes(Long fileSizeBytes) 
    {
        this.fileSizeBytes = fileSizeBytes;
    }

    public Long getFileSizeBytes() 
    {
        return fileSizeBytes;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("categoryType", getCategoryType())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .append("fileType", getFileType())
            .append("fileSizeBytes", getFileSizeBytes())
            .append("remarks", getRemarks())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}

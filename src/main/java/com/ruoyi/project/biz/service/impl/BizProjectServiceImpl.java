package com.ruoyi.project.biz.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.biz.mapper.BizProjectMapper;
import com.ruoyi.project.biz.domain.BizProject;
import com.ruoyi.project.biz.service.IBizProjectService;

/**
 * 项目管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class BizProjectServiceImpl implements IBizProjectService 
{
    @Autowired
    private BizProjectMapper bizProjectMapper;

    /**
     * 查询项目管理
     * 
     * @param id 项目管理主键
     * @return 项目管理
     */
    @Override
    public BizProject selectBizProjectById(Long id)
    {
        return bizProjectMapper.selectBizProjectById(id);
    }

    /**
     * 查询项目管理列表
     * 
     * @param bizProject 项目管理
     * @return 项目管理
     */
    @Override
    public List<BizProject> selectBizProjectList(BizProject bizProject)
    {
        return bizProjectMapper.selectBizProjectList(bizProject);
    }

    /**
     * 新增项目管理
     * 
     * @param bizProject 项目管理
     * @return 结果
     */
    @Override
    public int insertBizProject(BizProject bizProject)
    {
        bizProject.setCreateTime(DateUtils.getNowDate());
        return bizProjectMapper.insertBizProject(bizProject);
    }

    /**
     * 修改项目管理
     * 
     * @param bizProject 项目管理
     * @return 结果
     */
    @Override
    public int updateBizProject(BizProject bizProject)
    {
        bizProject.setUpdateTime(DateUtils.getNowDate());
        return bizProjectMapper.updateBizProject(bizProject);
    }

    /**
     * 批量删除项目管理
     * 
     * @param ids 需要删除的项目管理主键
     * @return 结果
     */
    @Override
    public int deleteBizProjectByIds(Long[] ids)
    {
        return bizProjectMapper.deleteBizProjectByIds(ids);
    }

    /**
     * 删除项目管理信息
     * 
     * @param id 项目管理主键
     * @return 结果
     */
    @Override
    public int deleteBizProjectById(Long id)
    {
        return bizProjectMapper.deleteBizProjectById(id);
    }
}

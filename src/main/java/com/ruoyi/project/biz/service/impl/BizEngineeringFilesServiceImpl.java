package com.ruoyi.project.biz.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.biz.mapper.BizEngineeringFilesMapper;
import com.ruoyi.project.biz.domain.BizEngineeringFiles;
import com.ruoyi.project.biz.service.IBizEngineeringFilesService;

/**
 * 工程数量文件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class BizEngineeringFilesServiceImpl implements IBizEngineeringFilesService 
{
    @Autowired
    private BizEngineeringFilesMapper bizEngineeringFilesMapper;

    /**
     * 查询工程数量文件
     * 
     * @param id 工程数量文件主键
     * @return 工程数量文件
     */
    @Override
    public BizEngineeringFiles selectBizEngineeringFilesById(Long id)
    {
        return bizEngineeringFilesMapper.selectBizEngineeringFilesById(id);
    }

    /**
     * 查询工程数量文件列表
     * 
     * @param bizEngineeringFiles 工程数量文件
     * @return 工程数量文件
     */
    @Override
    public List<BizEngineeringFiles> selectBizEngineeringFilesList(BizEngineeringFiles bizEngineeringFiles)
    {
        return bizEngineeringFilesMapper.selectBizEngineeringFilesList(bizEngineeringFiles);
    }

    /**
     * 新增工程数量文件
     * 
     * @param bizEngineeringFiles 工程数量文件
     * @return 结果
     */
    @Override
    public int insertBizEngineeringFiles(BizEngineeringFiles bizEngineeringFiles)
    {
        bizEngineeringFiles.setCreateTime(DateUtils.getNowDate());
        return bizEngineeringFilesMapper.insertBizEngineeringFiles(bizEngineeringFiles);
    }

    /**
     * 修改工程数量文件
     * 
     * @param bizEngineeringFiles 工程数量文件
     * @return 结果
     */
    @Override
    public int updateBizEngineeringFiles(BizEngineeringFiles bizEngineeringFiles)
    {
        bizEngineeringFiles.setUpdateTime(DateUtils.getNowDate());
        return bizEngineeringFilesMapper.updateBizEngineeringFiles(bizEngineeringFiles);
    }

    /**
     * 批量删除工程数量文件
     * 
     * @param ids 需要删除的工程数量文件主键
     * @return 结果
     */
    @Override
    public int deleteBizEngineeringFilesByIds(Long[] ids)
    {
        return bizEngineeringFilesMapper.deleteBizEngineeringFilesByIds(ids);
    }

    /**
     * 删除工程数量文件信息
     * 
     * @param id 工程数量文件主键
     * @return 结果
     */
    @Override
    public int deleteBizEngineeringFilesById(Long id)
    {
        return bizEngineeringFilesMapper.deleteBizEngineeringFilesById(id);
    }
}

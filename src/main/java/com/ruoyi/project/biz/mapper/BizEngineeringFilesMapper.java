package com.ruoyi.project.biz.mapper;

import java.util.List;
import com.ruoyi.project.biz.domain.BizEngineeringFiles;

/**
 * 工程数量文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface BizEngineeringFilesMapper 
{
    /**
     * 查询工程数量文件
     * 
     * @param id 工程数量文件主键
     * @return 工程数量文件
     */
    public BizEngineeringFiles selectBizEngineeringFilesById(Long id);

    /**
     * 查询工程数量文件列表
     * 
     * @param bizEngineeringFiles 工程数量文件
     * @return 工程数量文件集合
     */
    public List<BizEngineeringFiles> selectBizEngineeringFilesList(BizEngineeringFiles bizEngineeringFiles);

    /**
     * 新增工程数量文件
     * 
     * @param bizEngineeringFiles 工程数量文件
     * @return 结果
     */
    public int insertBizEngineeringFiles(BizEngineeringFiles bizEngineeringFiles);

    /**
     * 修改工程数量文件
     * 
     * @param bizEngineeringFiles 工程数量文件
     * @return 结果
     */
    public int updateBizEngineeringFiles(BizEngineeringFiles bizEngineeringFiles);

    /**
     * 删除工程数量文件
     * 
     * @param id 工程数量文件主键
     * @return 结果
     */
    public int deleteBizEngineeringFilesById(Long id);

    /**
     * 批量删除工程数量文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizEngineeringFilesByIds(Long[] ids);
}

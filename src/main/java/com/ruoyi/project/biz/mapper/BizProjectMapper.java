package com.ruoyi.project.biz.mapper;

import java.util.List;
import com.ruoyi.project.biz.domain.BizProject;

/**
 * 项目管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface BizProjectMapper 
{
    /**
     * 查询项目管理
     * 
     * @param id 项目管理主键
     * @return 项目管理
     */
    public BizProject selectBizProjectById(Long id);

    /**
     * 查询项目管理列表
     * 
     * @param bizProject 项目管理
     * @return 项目管理集合
     */
    public List<BizProject> selectBizProjectList(BizProject bizProject);

    /**
     * 新增项目管理
     * 
     * @param bizProject 项目管理
     * @return 结果
     */
    public int insertBizProject(BizProject bizProject);

    /**
     * 修改项目管理
     * 
     * @param bizProject 项目管理
     * @return 结果
     */
    public int updateBizProject(BizProject bizProject);

    /**
     * 删除项目管理
     * 
     * @param id 项目管理主键
     * @return 结果
     */
    public int deleteBizProjectById(Long id);

    /**
     * 批量删除项目管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizProjectByIds(Long[] ids);
}

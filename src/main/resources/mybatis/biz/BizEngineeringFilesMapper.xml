<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.biz.mapper.BizEngineeringFilesMapper">
    
    <resultMap type="BizEngineeringFiles" id="BizEngineeringFilesResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="categoryType"    column="category_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSizeBytes"    column="file_size_bytes"    />
        <result property="remarks"    column="remarks"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectBizEngineeringFilesVo">
        select id, project_id, category_type, file_name, file_path, file_type, file_size_bytes, remarks, create_time, create_by, update_time, update_by from biz_engineering_files
    </sql>

    <select id="selectBizEngineeringFilesList" parameterType="BizEngineeringFiles" resultMap="BizEngineeringFilesResult">
        <include refid="selectBizEngineeringFilesVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="categoryType != null "> and category_type = #{categoryType}</if>
        </where>
    </select>
    
    <select id="selectBizEngineeringFilesById" parameterType="Long" resultMap="BizEngineeringFilesResult">
        <include refid="selectBizEngineeringFilesVo"/>
        where id = #{id}
    </select>

    <insert id="insertBizEngineeringFiles" parameterType="BizEngineeringFiles" useGeneratedKeys="true" keyProperty="id">
        insert into biz_engineering_files
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="categoryType != null">category_type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileSizeBytes != null">file_size_bytes,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="categoryType != null">#{categoryType},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileSizeBytes != null">#{fileSizeBytes},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateBizEngineeringFiles" parameterType="BizEngineeringFiles">
        update biz_engineering_files
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="categoryType != null">category_type = #{categoryType},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileSizeBytes != null">file_size_bytes = #{fileSizeBytes},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizEngineeringFilesById" parameterType="Long">
        delete from biz_engineering_files where id = #{id}
    </delete>

    <delete id="deleteBizEngineeringFilesByIds" parameterType="String">
        delete from biz_engineering_files where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.biz.mapper.BizProjectMapper">
    
    <resultMap type="BizProject" id="BizProjectResult">
        <result property="id"    column="id"    />
        <result property="projectName"    column="project_name"    />
        <result property="constructionUnit"    column="construction_unit"    />
        <result property="supervisionUnit"    column="supervision_unit"    />
        <result property="remarks"    column="remarks"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectBizProjectVo">
        select id, project_name, construction_unit, supervision_unit, remarks, create_time, create_by, update_time, update_by from biz_project
    </sql>

    <select id="selectBizProjectList" parameterType="BizProject" resultMap="BizProjectResult">
        <include refid="selectBizProjectVo"/>
        <where>  
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="constructionUnit != null  and constructionUnit != ''"> and construction_unit = #{constructionUnit}</if>
            <if test="supervisionUnit != null  and supervisionUnit != ''"> and supervision_unit = #{supervisionUnit}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectBizProjectById" parameterType="Long" resultMap="BizProjectResult">
        <include refid="selectBizProjectVo"/>
        where id = #{id}
    </select>

    <insert id="insertBizProject" parameterType="BizProject" useGeneratedKeys="true" keyProperty="id">
        insert into biz_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="constructionUnit != null">construction_unit,</if>
            <if test="supervisionUnit != null">supervision_unit,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="constructionUnit != null">#{constructionUnit},</if>
            <if test="supervisionUnit != null">#{supervisionUnit},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateBizProject" parameterType="BizProject">
        update biz_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="constructionUnit != null">construction_unit = #{constructionUnit},</if>
            <if test="supervisionUnit != null">supervision_unit = #{supervisionUnit},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizProjectById" parameterType="Long">
        delete from biz_project where id = #{id}
    </delete>

    <delete id="deleteBizProjectByIds" parameterType="String">
        delete from biz_project where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
<template>
  <div class="enhanced-file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :limit="limit"
      :accept="accept"
      :headers="headers"
      :show-file-list="false"
      :auto-upload="true"
      drag
    >
      <div class="upload-dragger-content">
        <el-icon class="upload-icon"><UploadFilled /></el-icon>
        <div class="upload-text">
          <p>将文件拖到此处，或<em>点击上传</em></p>
        </div>
      </div>
    </el-upload>
    
    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div v-for="(file, index) in fileList" :key="file.uid" class="file-item">
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-details">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <span class="file-type">{{ getFileExtension(file.name) }}</span>
            </div>
          </div>
        </div>
        <div class="file-actions">
          <el-button 
            type="danger" 
            link 
            @click="handleRemove(index)"
            :icon="Delete"
          >删除</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"

const props = defineProps({
  modelValue: [String, Array],
  // 文件类型限制
  accept: {
    type: String,
    default: ""
  },
  // 文件数量限制
  limit: {
    type: Number,
    default: 1
  },
  // 文件大小限制(MB)
  fileSize: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['update:modelValue', 'file-info-change'])

const { proxy } = getCurrentInstance()
const uploadRef = ref()
const fileList = ref([])
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload")
const headers = ref({ Authorization: "Bearer " + getToken() })

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  if (val && val !== fileList.value.map(f => f.url).join(',')) {
    // 如果有初始值，解析并设置文件列表
    if (typeof val === 'string' && val) {
      const urls = val.split(',')
      fileList.value = urls.map((url, index) => ({
        uid: Date.now() + index,
        name: getFileNameFromUrl(url),
        url: url,
        status: 'success'
      }))
    }
  }
}, { immediate: true })

// 上传前检查
function handleBeforeUpload(file) {
  // 检查文件大小
  if (file.size / 1024 / 1024 > props.fileSize) {
    proxy.$modal.msgError(`文件大小不能超过 ${props.fileSize}MB!`)
    return false
  }
  
  // 检查文件类型
  if (props.accept) {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    if (!acceptTypes.includes(fileExtension)) {
      proxy.$modal.msgError(`只能上传 ${props.accept} 格式的文件!`)
      return false
    }
  }
  
  proxy.$modal.loading("正在上传文件，请稍候...")
  return true
}

// 上传成功
function handleUploadSuccess(response, file) {
  proxy.$modal.closeLoading()
  
  if (response.code === 200) {
    const newFile = {
      uid: file.uid,
      name: file.name,
      url: response.fileName,
      size: file.size,
      type: getFileExtension(file.name),
      status: 'success'
    }
    
    if (props.limit === 1) {
      fileList.value = [newFile]
    } else {
      fileList.value.push(newFile)
    }
    
    updateModelValue()
    
    // 发送文件信息变化事件
    emit('file-info-change', {
      name: file.name,
      size: file.size,
      type: getFileExtension(file.name),
      url: response.fileName
    })
    
    proxy.$modal.msgSuccess("上传成功")
  } else {
    proxy.$modal.msgError(response.msg || "上传失败")
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.closeLoading()
  proxy.$modal.msgError("上传失败")
}

// 文件数量超出限制
function handleExceed() {
  proxy.$modal.msgError(`最多只能上传 ${props.limit} 个文件!`)
}

// 删除文件
function handleRemove(index) {
  fileList.value.splice(index, 1)
  updateModelValue()
  
  // 如果删除后没有文件，清空文件信息
  if (fileList.value.length === 0) {
    emit('file-info-change', null)
  }
}

// 更新modelValue
function updateModelValue() {
  const urls = fileList.value.map(file => file.url).join(',')
  emit('update:modelValue', urls)
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件扩展名
function getFileExtension(fileName) {
  return fileName.split('.').pop().toLowerCase()
}

// 从URL获取文件名
function getFileNameFromUrl(url) {
  return url.split('/').pop()
}
</script>

<style scoped lang="scss">
.enhanced-file-upload {
  .upload-dragger-content {
    padding: 40px 20px;
    text-align: center;
    
    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }
    
    .upload-text {
      color: #606266;
      font-size: 14px;
      
      em {
        color: #409eff;
        font-style: normal;
      }
    }
  }
  
  .file-list {
    margin-top: 16px;
    
    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: #f9f9f9;
      
      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        
        .file-icon {
          font-size: 24px;
          color: #409eff;
          margin-right: 12px;
        }
        
        .file-details {
          .file-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .file-meta {
            font-size: 12px;
            color: #909399;
            
            .file-size {
              margin-right: 12px;
            }
            
            .file-type {
              text-transform: uppercase;
            }
          }
        }
      }
      
      .file-actions {
        margin-left: 12px;
      }
    }
  }
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  width: 100%;
  height: auto;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  
  &:hover {
    border-color: #409eff;
  }
}

:deep(.el-upload-dragger.is-dragover) {
  background-color: rgba(64, 158, 255, 0.06);
  border: 2px dashed #409eff;
}
</style>

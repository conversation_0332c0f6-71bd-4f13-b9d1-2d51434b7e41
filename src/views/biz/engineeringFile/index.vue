<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关联的项目ID" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入关联的项目ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属分类" prop="categoryType">
        <el-select v-model="queryParams.categoryType" placeholder="请选择所属分类" clearable>
          <el-option
            v-for="dict in category_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['biz:engineeringFile:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:engineeringFile:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:engineeringFile:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['biz:engineeringFile:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="engineeringFileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="关联的项目ID" align="center" prop="projectId" />
      <el-table-column label="所属分类" align="center" prop="categoryType">
        <template #default="scope">
          <dict-tag :options="category_type" :value="scope.row.categoryType"/>
        </template>
      </el-table-column>
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="文件存储路径" align="center" prop="filePath" />
      <el-table-column label="文件类型 " align="center" prop="fileType" />
      <el-table-column label="文件大小 (bytes)" align="center" prop="fileSizeBytes" />
      <el-table-column label="文件备注" align="center" prop="remarks" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:engineeringFile:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:engineeringFile:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工程数量文件对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="engineeringFileRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的项目ID" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入关联的项目ID" />
        </el-form-item>
        <el-form-item label="所属分类" prop="categoryType">
          <el-select v-model="form.categoryType" placeholder="请选择所属分类">
            <el-option
              v-for="dict in category_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入文件名称" />
        </el-form-item>
        <el-form-item label="文件存储路径" prop="filePath">
          <file-upload v-model="form.filePath"/>
        </el-form-item>
        <el-form-item label="文件大小 (bytes)" prop="fileSizeBytes">
          <el-input v-model="form.fileSizeBytes" placeholder="请输入文件大小 (bytes)" />
        </el-form-item>
        <el-form-item label="文件备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EngineeringFile">
import { listEngineeringFile, getEngineeringFile, delEngineeringFile, addEngineeringFile, updateEngineeringFile } from "@/api/biz/engineeringFile"

const { proxy } = getCurrentInstance()
const { category_type } = proxy.useDict('category_type')

const engineeringFileList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    categoryType: null,
  },
  rules: {
    projectId: [
      { required: true, message: "关联的项目ID不能为空", trigger: "blur" }
    ],
    categoryType: [
      { required: true, message: "所属分类不能为空", trigger: "change" }
    ],
    fileName: [
      { required: true, message: "文件名称不能为空", trigger: "blur" }
    ],
    filePath: [
      { required: true, message: "文件存储路径不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询工程数量文件列表 */
function getList() {
  loading.value = true
  listEngineeringFile(queryParams.value).then(response => {
    engineeringFileList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectId: null,
    categoryType: null,
    fileName: null,
    filePath: null,
    fileType: null,
    fileSizeBytes: null,
    remarks: null,
    createTime: null,
    createBy: null,
    updateTime: null,
    updateBy: null
  }
  proxy.resetForm("engineeringFileRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加工程数量文件"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getEngineeringFile(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改工程数量文件"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["engineeringFileRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateEngineeringFile(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addEngineeringFile(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除工程数量文件编号为"' + _ids + '"的数据项？').then(function() {
    return delEngineeringFile(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('biz/engineeringFile/export', {
    ...queryParams.value
  }, `engineeringFile_${new Date().getTime()}.xlsx`)
}

getList()
</script>

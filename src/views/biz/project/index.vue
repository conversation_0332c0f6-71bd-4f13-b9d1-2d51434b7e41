<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建设单位" prop="constructionUnit">
        <el-input
          v-model="queryParams.constructionUnit"
          placeholder="请输入建设单位"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监理单位" prop="supervisionUnit">
        <el-input
          v-model="queryParams.supervisionUnit"
          placeholder="请输入监理单位"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['biz:project:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:project:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:project:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['biz:project:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="建设单位" align="center" prop="constructionUnit" />
      <el-table-column label="监理单位" align="center" prop="supervisionUnit" />
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新者" align="center" prop="updateBy" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['biz:project:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['biz:project:remove']">删除</el-button>
          <el-button link type="primary" icon="Document" @click="handleEngineeringFiles(scope.row)" v-hasPermi="['biz:engineeringFile:list']">维护工程数量表</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="projectRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="建设单位" prop="constructionUnit">
          <el-input v-model="form.constructionUnit" placeholder="请输入建设单位" />
        </el-form-item>
        <el-form-item label="监理单位" prop="supervisionUnit">
          <el-input v-model="form.supervisionUnit" placeholder="请输入监理单位" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工程数量表管理对话框 -->
    <el-dialog :title="`工程数量表管理 - ${currentProjectName}`" v-model="engineeringFilesOpen" width="1400px" append-to-body>
      <div class="engineering-files-container">
        <el-row :gutter="20">
          <!-- 左侧分类树 -->
          <el-col :span="6">
            <div class="category-tree-container">
              <div class="category-header">
                <h4>分类名称</h4>
              </div>
              <div class="category-tree">
                <div
                  class="category-item"
                  :class="{ active: selectedCategoryType === null }"
                  @click="handleCategorySelect(null)"
                >
                  <el-icon><Folder /></el-icon>
                  <span>全部分类</span>
                </div>
                <div
                  v-for="dict in category_type"
                  :key="dict.value"
                  class="category-item"
                  :class="{ active: selectedCategoryType === dict.value }"
                  @click="handleCategorySelect(dict.value)"
                >
                  <el-icon><Document /></el-icon>
                  <span>{{ dict.label }}</span>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右侧文件列表 -->
          <el-col :span="18">
            <div class="file-list-container">
              <div class="file-header">
                <h4>{{ selectedCategoryLabel || '全部分类' }} - 文件列表</h4>
                <div class="file-actions">
                  <el-button
                    type="primary"
                    icon="Plus"
                    @click="handleEngineeringAdd"
                    v-hasPermi="['biz:engineeringFile:add']"
                    :disabled="!selectedCategoryType"
                  >上传文件</el-button>
                  <el-button
                    type="success"
                    icon="Edit"
                    :disabled="engineeringSingle"
                    @click="handleEngineeringUpdate"
                    v-hasPermi="['biz:engineeringFile:edit']"
                  >修改</el-button>
                  <el-button
                    type="danger"
                    icon="Delete"
                    :disabled="engineeringMultiple"
                    @click="handleEngineeringDelete"
                    v-hasPermi="['biz:engineeringFile:remove']"
                  >删除</el-button>
                </div>
              </div>

              <el-table v-loading="engineeringLoading" :data="engineeringFileList" @selection-change="handleEngineeringSelectionChange" height="400">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="文件名称" align="center" prop="fileName" min-width="150" />
                <el-table-column label="文件类型" align="center" prop="fileType" width="100" />
                <el-table-column label="文件大小" align="center" prop="fileSizeBytes" width="120">
                  <template #default="scope">
                    {{ formatFileSize(scope.row.fileSizeBytes) }}
                  </template>
                </el-table-column>
                <el-table-column label="文件备注" align="center" prop="remarks" min-width="150" show-overflow-tooltip />
                <el-table-column label="上传时间" align="center" prop="createTime" width="180">
                  <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
                  <template #default="scope">
                    <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)">下载</el-button>
                    <el-button link type="primary" icon="Edit" @click="handleEngineeringUpdate(scope.row)" v-hasPermi="['biz:engineeringFile:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleEngineeringDelete(scope.row)" v-hasPermi="['biz:engineeringFile:remove']">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="engineeringTotal>0"
                :total="engineeringTotal"
                v-model:page="engineeringQueryParams.pageNum"
                v-model:limit="engineeringQueryParams.pageSize"
                @pagination="getEngineeringList"
                style="margin-top: 10px;"
              />
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="engineeringFilesOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改工程数量文件对话框 -->
    <el-dialog :title="engineeringTitle" v-model="engineeringFormOpen" width="600px" append-to-body>
      <el-form ref="engineeringFileRef" :model="engineeringForm" :rules="engineeringRules" label-width="100px">
        <el-form-item label="所属分类" prop="categoryType">
          <el-select v-model="engineeringForm.categoryType" placeholder="请选择所属分类" :disabled="isEditMode">
            <el-option
              v-for="dict in category_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件上传" prop="filePath" v-if="!isEditMode">
          <enhanced-file-upload
            v-model="engineeringForm.filePath"
            @file-info-change="handleFileInfoChange"
            :accept="'.doc,.docx,.xls,.xlsx,.pdf,.txt'"
            :limit="1"
          />
          <div class="upload-tip">
            支持上传 .doc, .docx, .xls, .xlsx, .pdf, .txt 格式文件，单个文件不超过10MB
          </div>
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="engineeringForm.fileName" placeholder="文件上传后自动填充" :readonly="!isEditMode" />
        </el-form-item>
        <el-form-item label="文件类型" prop="fileType">
          <el-input v-model="engineeringForm.fileType" placeholder="文件上传后自动填充" readonly />
        </el-form-item>
        <el-form-item label="文件大小" prop="fileSizeBytes">
          <el-input v-model="engineeringForm.fileSizeDisplay" placeholder="文件上传后自动填充" readonly />
        </el-form-item>
        <el-form-item label="文件备注" prop="remarks">
          <el-input v-model="engineeringForm.remarks" type="textarea" :rows="3" placeholder="请输入文件备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitEngineeringForm">确 定</el-button>
          <el-button @click="cancelEngineering">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Project">
import { listProject, getProject, delProject, addProject, updateProject } from "@/api/biz/project"
import { listEngineeringFile, getEngineeringFile, delEngineeringFile, addEngineeringFile, updateEngineeringFile } from "@/api/biz/engineeringFile"

const { proxy } = getCurrentInstance()
const { category_type } = proxy.useDict('category_type')

const projectList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

// 工程文件管理相关数据
const engineeringFileList = ref([])
const engineeringFilesOpen = ref(false)
const engineeringFormOpen = ref(false)
const engineeringLoading = ref(true)
const engineeringIds = ref([])
const engineeringSingle = ref(true)
const engineeringMultiple = ref(true)
const engineeringTotal = ref(0)
const engineeringTitle = ref("")
const currentProjectId = ref(null)
const currentProjectName = ref("")
const selectedCategoryType = ref(null)
const selectedCategoryLabel = ref("")
const isEditMode = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: null,
    constructionUnit: null,
    supervisionUnit: null,
    remarks: null,
  },
  rules: {
    projectName: [
      { required: true, message: "项目名称不能为空", trigger: "blur" }
    ],
  }
})

// 工程文件管理相关数据
const engineeringData = reactive({
  engineeringForm: {},
  engineeringQueryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    categoryType: null,
  },
  engineeringRules: {
    categoryType: [
      { required: true, message: "所属分类不能为空", trigger: "change" }
    ],
    fileName: [
      { required: true, message: "文件名称不能为空", trigger: "blur" }
    ],
    filePath: [
      { required: true, message: "文件存储路径不能为空", trigger: "blur" }
    ],
    remarks: [
      { max: 500, message: "备注信息不能超过500个字符", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)
const { engineeringQueryParams, engineeringForm, engineeringRules } = toRefs(engineeringData)

/** 查询项目管理列表 */
function getList() {
  loading.value = true
  listProject(queryParams.value).then(response => {
    projectList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectName: null,
    constructionUnit: null,
    supervisionUnit: null,
    remarks: null,
    createTime: null,
    createBy: null,
    updateTime: null,
    updateBy: null
  }
  proxy.resetForm("projectRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加项目管理"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getProject(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改项目管理"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["projectRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateProject(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addProject(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除项目管理编号为"' + _ids + '"的数据项？').then(function() {
    return delProject(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('biz/project/export', {
    ...queryParams.value
  }, `project_${new Date().getTime()}.xlsx`)
}

// ==================== 工程文件管理相关函数 ====================

/** 维护工程数量表按钮操作 */
function handleEngineeringFiles(row) {
  currentProjectId.value = row.id
  currentProjectName.value = row.projectName
  engineeringQueryParams.value.projectId = row.id
  engineeringFilesOpen.value = true
  getEngineeringList()
}

/** 查询工程数量文件列表 */
function getEngineeringList() {
  engineeringLoading.value = true
  listEngineeringFile(engineeringQueryParams.value).then(response => {
    engineeringFileList.value = response.rows
    engineeringTotal.value = response.total
    engineeringLoading.value = false
  })
}

/** 工程文件搜索按钮操作 */
function handleEngineeringQuery() {
  engineeringQueryParams.value.pageNum = 1
  getEngineeringList()
}

/** 工程文件重置按钮操作 */
function resetEngineeringQuery() {
  proxy.resetForm("engineeringQueryRef")
  engineeringQueryParams.value.projectId = currentProjectId.value
  handleEngineeringQuery()
}

// 工程文件多选框选中数据
function handleEngineeringSelectionChange(selection) {
  engineeringIds.value = selection.map(item => item.id)
  engineeringSingle.value = selection.length != 1
  engineeringMultiple.value = !selection.length
}

/** 工程文件新增按钮操作 */
function handleEngineeringAdd() {
  resetEngineering()
  engineeringFormOpen.value = true
  engineeringTitle.value = "添加工程数量文件"
}

/** 工程文件修改按钮操作 */
function handleEngineeringUpdate(row) {
  resetEngineering()
  const _id = row.id || engineeringIds.value
  getEngineeringFile(_id).then(response => {
    engineeringForm.value = response.data
    engineeringFormOpen.value = true
    engineeringTitle.value = "修改工程数量文件"
  })
}

// 工程文件表单重置
function resetEngineering() {
  engineeringForm.value = {
    id: null,
    projectId: currentProjectId.value,
    categoryType: null,
    fileName: null,
    filePath: null,
    fileType: null,
    fileSizeBytes: null,
    remarks: null,
    createTime: null,
    createBy: null,
    updateTime: null,
    updateBy: null
  }
  proxy.resetForm("engineeringFileRef")
}

// 工程文件取消按钮
function cancelEngineering() {
  engineeringFormOpen.value = false
  resetEngineering()
}

/** 工程文件提交按钮 */
function submitEngineeringForm() {
  proxy.$refs["engineeringFileRef"].validate(valid => {
    if (valid) {
      engineeringForm.value.projectId = currentProjectId.value
      if (engineeringForm.value.id != null) {
        updateEngineeringFile(engineeringForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          engineeringFormOpen.value = false
          getEngineeringList()
        })
      } else {
        addEngineeringFile(engineeringForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          engineeringFormOpen.value = false
          getEngineeringList()
        })
      }
    }
  })
}

/** 工程文件删除按钮操作 */
function handleEngineeringDelete(row) {
  const _ids = row.id || engineeringIds.value
  proxy.$modal.confirm('是否确认删除工程数量文件编号为"' + _ids + '"的数据项？').then(function() {
    return delEngineeringFile(_ids)
  }).then(() => {
    getEngineeringList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

getList()
</script>

<style scoped>
.engineering-files-container {
  padding: 10px 0;
}

.engineering-files-container .el-table {
  margin-top: 10px;
}

.engineering-files-container .mb8 {
  margin-bottom: 8px;
}
</style>

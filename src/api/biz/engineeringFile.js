import request from '@/utils/request'

// 查询工程数量文件列表
export function listEngineeringFile(query) {
  return request({
    url: '/biz/engineeringFile/list',
    method: 'get',
    params: query
  })
}

// 查询工程数量文件详细
export function getEngineeringFile(id) {
  return request({
    url: '/biz/engineeringFile/' + id,
    method: 'get'
  })
}

// 新增工程数量文件
export function addEngineeringFile(data) {
  return request({
    url: '/biz/engineeringFile',
    method: 'post',
    data: data
  })
}

// 修改工程数量文件
export function updateEngineeringFile(data) {
  return request({
    url: '/biz/engineeringFile',
    method: 'put',
    data: data
  })
}

// 删除工程数量文件
export function delEngineeringFile(id) {
  return request({
    url: '/biz/engineeringFile/' + id,
    method: 'delete'
  })
}

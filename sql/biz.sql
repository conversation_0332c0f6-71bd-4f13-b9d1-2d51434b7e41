-- Table for Projects (项目管理)
CREATE TABLE `biz_project` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `project_name` VARCHAR(255) NOT NULL COMMENT '项目名称',
    `construction_unit` VARCHAR(255) NULL COMMENT '建设单位',
    `supervision_unit` VARCHAR(255) NULL COMMENT '监理单位',
    `supervision_unit` VARCHAR(255) NULL COMMENT '监理单位',
    `remarks` TEXT NULL COMMENT '备注',
    `created_time` DATETIME COMMENT '创建时间',
    `create_by`   varchar(64)  default ''  null comment '创建者',
    `updated_time` DATETIME COMMENT '更新时间',
    `update_by`   varchar(64)  default ''  null comment '更新者'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';



-- Table for Project Files (工程数量表文件)
-- These are the files shown under each category in image_6cfb51.png
CREATE TABLE `project_files` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `project_id` BIGINT NOT NULL COMMENT '关联的项目ID',
    `category_type` BIGINT NOT NULL COMMENT '所属分类',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名称 (as displayed or input by user, e.g., xxx工程数量表.pdf)',
    `file_path` VARCHAR(1024) NOT NULL COMMENT '文件存储路径',
    `file_type` VARCHAR(50) NULL COMMENT '文件类型 (e.g., pdf, docx, xlsx)',
    `file_size_bytes` BIGBIGINT NULL COMMENT '文件大小 (bytes)',
    `remarks` TEXT NULL COMMENT '文件备注',
    `uploaded_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件分类表';
